import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowCircleRight2 } from 'iconsax-react';
import { useEffect } from 'react';
import del from '../../../assets/images/del.png';

export const DeleteEventSuccess = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const eventName = location.state?.eventName || 'Your Event';

  useEffect(() => {
    document.body.style.position = 'fixed';
    document.body.style.width = '100%';
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.position = '';
      document.body.style.width = '';
      document.body.style.overflow = '';
    };
  }, []);

  const handleBackToDashboard = () => {
    navigate('/', { replace: true });
  };

  const handleSendMessage = () => {
    // Navigate to guest messaging or implement messaging functionality
    // For now, we'll just navigate back to dashboard
    navigate('/', { replace: true });
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="min-h-screen pt-4 md:py-20 bg-[linear-gradient(180deg,_#FDEFE9_0%,_#FEF7F4_100%)] flex flex-col items-center justify-center relative">
        {/* <div className="absolute inset-0 opacity-30">
          <div className="w-full h-full bg-gradient-to-br from-pink-100 via-purple-50 to-blue-100 blur-sm" />
        </div> */}

        {/* Main Content Card */}
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="relative z-10 bg-white rounded-3xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
          {/* Success Icon Section */}
          {/* <div className="bg-white px-8 pt-12 pb-8 text-center">
            <div className="inline-flex items-center justify-center w-24 h-24 bg-white rounded-full shadow-lg mb-6">
              <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center">
                <CloseCircle size={32} color="#FFFFFF" variant="Bold" />
              </div>
            </div>
          </div> */}
          <img src={del} alt="" />
          {/* Content */}
          <div className="px-8 pb-8 text-center flex justify-center flex-col ">
            <div className=" max-w-[312px]  mx-auto">
              <h2 className="text-[36px] font-medium text-[#000026]">
                Your Event has
              </h2>
              <h3 className="text-[32px] font-medium text-[#808080] mb-3.5">
                been Deleted!
              </h3>

              <p className="text-[#808080] text-base leading-relaxed mb-8">
                You just deleted{' '}
                <span className="font-medium italic text-gray-700 text-base">
                  {eventName}
                </span>
                , would you like to send a personalized message to your
                Guestlist?
              </p>
            </div>
            <div className="flex flex-col gap-4">
              <button
                onClick={handleSendMessage}
                className="mx-auto px-2 h-[48px] bg-primary w-fit text-white rounded-full font-semibold text-base px-3.5 transition-colors flex items-center justify-center gap-2">
                <span>Send message to Guests</span>
                <ArrowCircleRight2 size={20} color="#FFFFFF" variant="Bulk" />
              </button>

              <button
                onClick={handleBackToDashboard}
                className="underline text-[#FF5519] italic font-bold text-lg cursor-pointer">
                Back to Dashboard
              </button>
            </div>
          </div>
        </motion.div>

        <button
          type="button"
          className="bg-[#EAE8FF] px-4 py-2.5 rounded-full font-bold text-sm cursor-pointer mt-[56px]">
          Create a new Event 🎉
        </button>
      </div>
    </div>
  );
};
