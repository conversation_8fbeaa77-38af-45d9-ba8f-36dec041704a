/* eslint-disable @typescript-eslint/no-explicit-any */
import { Gift, SearchStatus, UserAdd } from 'iconsax-react';
import { useState } from 'react';
import { SearchModal } from './search-modal';
import CreateGuestList from '../../pages/prelaunch/create-guest-list/create-guest';
import { CreateGiftRegistry } from '../../pages/prelaunch/gift-registry/create-gift-registry';

export const SubHead = ({
  completeEventData = {},
}: {
  completeEventData: any;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isGuestListModalOpen, setIsGuestListModalOpen] = useState(false);
  const [isGiftRegistryModalOpen, setIsGiftRegistryModalOpen] = useState(false);

  const toggleModal = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSearchQuery('');
    }
  };
  const totalGuests = completeEventData?.guest_count?.total ?? 0;
  const cashGift = completeEventData?.gift_count?.cash_gift ?? 0;
  const itemGift = completeEventData?.gift_count?.item_gift ?? 0;
  return (
    <div className=" px-4 md:px-0">
      {(totalGuests > 0 || cashGift > 0 || itemGift > 0) && (
        <div className="mb-8 ">
          <h2 className="hidden md:block italic font-semibold text-base text-grey-750 mb-3">
            Quick Actions
          </h2>
          <div className="flex justify-between md:hidden">
            <h2 className="italic font-semibold text-base text-grey-750 mb-3">
              Quick Actions
            </h2>
            <button
              type="button"
              onClick={toggleModal}
              className="bg-primary-250 cursor-pointer p-2.5 h-10 w-10 rounded-[64px] ">
              <SearchStatus size="20" color="#82A7E2" variant="Bulk" />
            </button>
          </div>
          <div className="flex justify-center md:justify-between">
            <div className="flex flex-col md:flex-row  items-center gap-2">
              {/* {totalGuests > 1 && (
                <button className="flex items-center gap-2 bg-white p-2.5 rounded-[64px] backdrop-blur-[12px]">
                  <UserAdd size="20" color="#9CC1FC" variant="Bulk" />
                  <span className="text-sm font-semibold text-dark-blue-300 italic">
                    Add Collaborator{' '}
                  </span>
                </button>
              )} */}
              {(cashGift > 0 || itemGift > 0) && (
                <button
                  onClick={() => setIsGiftRegistryModalOpen(true)}
                  className="flex items-center gap-2 cursor-pointer bg-white p-2.5 rounded-[64px] backdrop-blur-[12px]">
                  <Gift size="20" color="#FF6630" variant="Bulk" />
                  <span className="text-sm font-semibold italic">
                    Add gift to Registry{' '}
                  </span>
                </button>
              )}
              {totalGuests > 1 && (
                <button
                  type="button"
                  onClick={() => setIsGuestListModalOpen(true)}
                  className="flex items-center gap-2 bg-white p-2.5 rounded-[64px] backdrop-blur-[12px]">
                  <UserAdd size="20" color="#4D55F2" variant="Bulk" />
                  <span className="text-sm font-semibold text-dark-blue-300 italic">
                    Add New Guest{' '}
                  </span>
                </button>
              )}
            </div>
            <button
              type="button"
              onClick={toggleModal}
              className="hidden md:block bg-primary-250 cursor-pointer p-2.5 h-10 w-10 rounded-[64px] ">
              <SearchStatus size="20" color="#82A7E2" variant="Bulk" />
            </button>
          </div>
        </div>
      )}
      {/* Search Modal */}
      {isOpen && (
        <div className="z-30">
          <SearchModal
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            toggleModal={toggleModal}
          />
        </div>
      )}
      {/* Guest List Modal */}
      {isGuestListModalOpen && (
        <CreateGuestList onClose={() => setIsGuestListModalOpen(false)} />
      )}
      {isGiftRegistryModalOpen && (
        <CreateGiftRegistry onClose={() => setIsGiftRegistryModalOpen(false)} />
      )}
    </div>
  );
};
