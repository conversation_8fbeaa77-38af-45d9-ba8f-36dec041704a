import { motion, AnimatePresence } from 'framer-motion';
import { TickCircle } from 'iconsax-react';

interface EventSavedSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const EventSavedSuccessModal: React.FC<EventSavedSuccessModalProps> = ({
  isOpen,
  onClose,
}) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm"
          onClick={onClose}>
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="mx-4 w-full max-w-[500px] relative rounded-2xl bg-white shadow-xl sm:mx-0"
            onClick={(e) => e.stopPropagation()}>
            {/* Close Button */}
            {/* <button onClick={onClose} className="absolute right-4 top-4 z-10">
              <CloseCircle size={24} variant="Bulk" color="#9CA3AF" />
            </button> */}

            <div className="text-center">
              <div className="flex justify-center py-12 mb-8 rounded-t-2xl bg-[linear-gradient(180deg,_#EBF9EF_22.63%,_#F7FDF8_100%)]">
                <TickCircle size={160} color="#22C55E" variant="Bold" />
              </div>

              <h2 className="text-3xl font-semibold text-gray-900 mb-3">
                Your Event details
              </h2>
              <h3 className="text-3xl font-semibold text-gray-500 mb-6">
                has been saved!
              </h3>

              <p className="text-gray-600 text-base leading-relaxed max-w-sm mx-auto mb-8">
                Your Event details has been saved successfully and the
                information would be communicated with your guests
              </p>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
