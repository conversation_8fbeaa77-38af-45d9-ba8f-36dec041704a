import { motion, AnimatePresence } from 'framer-motion';
import { CloseCircle } from 'iconsax-react';

interface SwitchEventPreferenceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const SwitchEventPreferenceModal: React.FC<
  SwitchEventPreferenceModalProps
> = ({ isOpen, onClose, onConfirm }) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm"
          onClick={onClose}>
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="mx-4 w-full max-w-[500px] relative rounded-2xl bg-white shadow-xl sm:mx-0"
            onClick={(e) => e.stopPropagation()}>
            {/* Close Button */}
            <button onClick={onClose} className="absolute right-4 top-4 z-10">
              <CloseCircle size={24} variant="Bulk" color="#9CA3AF" />
            </button>

            {/* Modal Content */}
            <div className="px-8 py-10 text-center">
              {/* Icon */}
              <div className="flex justify-center mb-6">
                <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
                  <svg
                    width="40"
                    height="40"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="text-blue-600">
                    <path
                      d="M4.06189 13C4.53892 16.3923 7.27158 19 10.7616 19C12.4063 19 13.8857 18.3151 14.9694 17.2493L13.9 16.1799C13.1315 16.9484 12.0136 17.4 10.7616 17.4C8.1479 17.4 6.02339 15.2755 6.02339 12.6618C6.02339 11.4098 6.47495 10.292 7.24343 9.52343L6.17407 8.45407C5.10829 9.53777 4.42339 11.0172 4.42339 12.6618C4.42339 12.7756 4.43845 12.8858 4.46189 13H4.06189Z"
                      fill="currentColor"
                    />
                    <path
                      d="M19.9381 11C19.4611 7.60771 16.7284 5 13.2384 5C11.5937 5 10.1143 5.68485 9.03062 6.75063L10.1 7.82L10.1 7.82C10.8685 7.05157 11.9864 6.6 13.2384 6.6C15.8521 6.6 17.9766 8.72451 17.9766 11.3382C17.9766 12.5902 17.525 13.708 16.7566 14.4766L17.8259 15.5459C18.8917 14.4622 19.5766 12.9828 19.5766 11.3382C19.5766 11.2244 19.5615 11.1142 19.5381 11H19.9381Z"
                      fill="currentColor"
                    />
                    <path
                      d="M6.17407 8.45407L7.24343 9.52343C8.01191 8.75495 9.12971 8.30339 10.3817 8.30339C12.9954 8.30339 15.1199 10.4279 15.1199 13.0416C15.1199 14.2936 14.6683 15.4114 13.8999 16.1799L14.9692 17.2492C16.035 16.1655 16.7199 14.6861 16.7199 13.0416C16.7199 9.54388 13.8794 6.70339 10.3817 6.70339C8.73717 6.70339 7.25777 7.38829 6.17407 8.45407Z"
                      fill="currentColor"
                    />
                    <path
                      d="M2 12L4.5 9.5L7 12L4.5 14.5L2 12Z"
                      fill="currentColor"
                    />
                    <path
                      d="M22 12L19.5 14.5L17 12L19.5 9.5L22 12Z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
              </div>

              {/* Title */}
              <h2 className="text-2xl font-semibold text-gray-900 mb-3">
                Switch Event <span className="text-blue-600">Preference</span> ?
              </h2>

              {/* Description */}
              <p className="text-gray-600 text-base leading-relaxed mb-8 max-w-sm mx-auto">
                You are about to switch your event preference from private to
                open, this means that your event can be joined by anyone who has
                access to your event link.
              </p>

              {/* Action Buttons */}
              <div className="flex gap-3 justify-center">
                <button
                  onClick={onClose}
                  className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-full font-medium transition-colors">
                  No, Cancel
                </button>
                <button
                  onClick={onConfirm}
                  className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-full font-medium transition-colors">
                  Yes, Switch
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
