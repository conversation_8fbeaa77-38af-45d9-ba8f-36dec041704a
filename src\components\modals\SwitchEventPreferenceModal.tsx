import { motion, AnimatePresence } from 'framer-motion';
import { CloseCircle } from 'iconsax-react';
import exchange from '../../assets/images/switch.png';

interface SwitchEventPreferenceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

export const SwitchEventPreferenceModal: React.FC<
  SwitchEventPreferenceModalProps
> = ({ isOpen, onClose, onConfirm }) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm"
          onClick={onClose}>
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="mx-4 w-full max-w-[782px] relative rounded-2xl bg-white shadow-xl sm:mx-0"
            onClick={(e) => e.stopPropagation()}>
            {/* Close Button */}
            <button onClick={onClose} className="absolute right-4 top-4 z-10">
              <CloseCircle size={32} variant="Bulk" color="#634C42" />
            </button>

            {/* Modal Content */}
            <div className="flex flex-col lg:flex-row ">
              {/* Icon */}
              <div className="bg-gradient-to-b from-[#FDEFE9] to-[#FEF7F4] rounded-t-[20px] lg:rounded-t-[0px]  lg:rounded-l-[20px] flex justify-center items-center lg:max-w-[256px] w-full">
                <img src={exchange} alt="switch-type" />
              </div>

              <div className="py-8 md:ml-8  px-2 md:px-0 text-center lg:text-start lg:max-w-[450px]">
                {/* Title */}
                <h2 className="text-2xl md:text-[36px] font-semibold text-gray-900 mb-3">
                  Switch Event <span className="text-blue-600">Preference</span>{' '}
                  ?
                </h2>

                {/* Description */}
                <p className="text-gray-600 text-base md:text-xl leading-relaxed mb-8">
                  You are about to switch your event preference from private to
                  open, this means that your event can be joined by anyone who
                  has access to your event link.
                </p>

                {/* Action Buttons */}
                <div className="flex gap-3 ">
                  <button
                    onClick={onClose}
                    className="px-6 py-3 text-[#000026] bg-[#FDF2ED]  rounded-full font-medium transition-colors">
                    No, Cancel
                  </button>
                  <button
                    onClick={onConfirm}
                    className="px-6 py-3 bg-[#4D55F2]  text-white rounded-full font-medium transition-colors">
                    Yes, Switch
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
