import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, Clock, CloseCircle, Edit2, Location } from 'iconsax-react';
import { AddressAutocomplete } from '../inputs/address-autocomplete';
import { EditEventFormData } from '../../types/editEvent';

interface EditEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  formData: EditEventFormData;
  onInputChange: (field: string, value: string) => void;
  onSave: () => void;
  isLoading: boolean;
  dateRangeText: string;
  onDatePickerOpen: () => void;
  showTimePicker: boolean;
  onTimePickerToggle: () => void;
  timeOptions: string[];
  onTimeSelect: (time: string) => void;
  timePickerRef: React.RefObject<HTMLDivElement | null>;
  onPreferenceChangeRequest: (newPreference: string) => void;
}

export const EditEventModal: React.FC<EditEventModalProps> = ({
  isOpen,
  onClose,
  formData,
  onInputChange,
  onSave,
  isLoading,
  dateRangeText,
  onDatePickerOpen,
  showTimePicker,
  onTimePickerToggle,
  timeOptions,
  onTimeSelect,
  timePickerRef,
  onPreferenceChangeRequest,
}) => {
  // Handle preference change with confirmation for private to public switch
  const handlePreferenceChange = (newPreference: string) => {
    // If switching from Private Event to Open Event, show confirmation modal
    if (
      formData.preference === 'Private Event' &&
      newPreference === 'Open Event'
    ) {
      onPreferenceChangeRequest(newPreference);
    } else {
      // For other changes, update directly
      onInputChange('preference', newPreference);
    }
  };
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm"
          onClick={onClose}>
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ duration: 0.2 }}
            className="mx-4 max-h-[90vh] w-full max-w-[604px] px-12  relative overflow-y-auto [&::-webkit-scrollbar]:hidden rounded-2xl bg-white shadow-xl sm:mx-0"
            onClick={(e) => e.stopPropagation()}>
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 pb-4">
              <h3 className="text-xl font-semibold text-gray-900">
                Edit Event Details
              </h3>
            </div>
            <button onClick={onClose} className=" absolute right-3 top-2">
              <CloseCircle size={32} variant="Bulk" color="#4D55F2" />
            </button>
            {/* Modal Content */}
            <div className="px-6 pb-6 space-y-5">
              {/* Event Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Event Name
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.eventName}
                    onChange={(e) => onInputChange('eventName', e.target.value)}
                    placeholder="Enter event name"
                    className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
                  />
                  <Edit2
                    size={16}
                    variant="Bulk"
                    color="#4D55F2"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  />
                </div>
              </div>

              {/* Event Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Event Description
                </label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="e.g bridesmaid celebration for all ladies"
                    value={formData.eventDescription}
                    onChange={(e) =>
                      onInputChange('eventDescription', e.target.value)
                    }
                    className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base"
                  />
                  <Edit2
                    size={16}
                    variant="Bulk"
                    color="#4D55F2"
                    className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  />
                </div>
              </div>

              {/* Event Date and Time */}
              <div className="flex gap-3">
                {/* Event Date */}
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Date
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={dateRangeText}
                      readOnly
                      onClick={onDatePickerOpen}
                      className="w-full py-2.5 px-3.5 pl-10 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                      placeholder="Select date"
                    />
                    <Calendar
                      size={18}
                      color="#4D55F2"
                      variant="Bulk"
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                      onClick={onDatePickerOpen}
                    />
                  </div>
                </div>

                {/* Time of Event */}
                <div className="w-[130px]">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Time of Event
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={formData.eventTime}
                      readOnly
                      onClick={onTimePickerToggle}
                      className="w-full py-2.5 pl-10  rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                      placeholder="Select time"
                    />
                    <Clock
                      size={18}
                      color="#4D55F2"
                      variant="Bulk"
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                      onClick={onTimePickerToggle}
                    />

                    {/* Time Picker Dropdown */}
                    {showTimePicker && (
                      <div
                        ref={timePickerRef}
                        className="absolute z-10 mt-1 bg-white rounded-xl shadow-lg border border-gray-200 left-0 w-full max-h-[200px] overflow-y-auto">
                        <div className="py-2">
                          {timeOptions.map((time) => (
                            <button
                              key={time}
                              onClick={() => onTimeSelect(time)}
                              className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-50 ${
                                formData.eventTime === time
                                  ? 'bg-blue-50 text-blue-600 font-medium'
                                  : 'text-gray-700'
                              }`}>
                              {time}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Preference */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Preference
                </label>
                <div className="relative">
                  <select
                    value={formData.preference}
                    onChange={(e) => handlePreferenceChange(e.target.value)}
                    className="w-full py-3 px-4 pr-10 rounded-full border border-gray-200 outline-none text-sm cursor-pointer  appearance-none bg-white"
                    style={{
                      backgroundImage: `url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234D55F2' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e")`,
                      backgroundRepeat: 'no-repeat',
                      backgroundPosition: 'right 12px center',
                      backgroundSize: '16px',
                    }}>
                    <option value="Open Event">Open Event</option>
                    <option value="Private Event">Private Event</option>
                    <option value="Invite Only">Invite Only</option>
                  </select>

                  {/* Switch to Open text */}
                  {formData.preference === 'Private Event' && (
                    <div
                      className="absolute right-12 top-1/2 transform -translate-y-1/2 cursor-pointer"
                      onClick={() => handlePreferenceChange('Open Event')}>
                      <span className="text-xs text-blue-500 font-medium hover:text-blue-600 transition-colors">
                        Switch to Open
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Location
                </label>
                <div className="relative">
                  <div className="relative">
                    <AddressAutocomplete
                      value={formData.location}
                      onChange={(value) => onInputChange('location', value)}
                      placeholder="Enter event location"
                      className="w-full py-3 px-4 pl-10 pr-10 rounded-full border border-gray-200 placeholder:text-gray-400 outline-none text-sm "
                    />
                    <Location
                      size={18}
                      color="#4D55F2"
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none"
                    />
                    <Edit2
                      size={16}
                      variant="Bulk"
                      color="#4D55F2"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="p-6 pt-4">
              <button
                onClick={onSave}
                disabled={isLoading}
                className="w-full bg-primary text-white py-3.5 px-6 rounded-full font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm">
                {isLoading ? 'Saving Changes...' : 'Save Changes'}
              </button>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
